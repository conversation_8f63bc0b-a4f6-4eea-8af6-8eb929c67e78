import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/behavioral_data_model.dart';

class FirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> logPasteAttempt(String userId) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      await _firestore
          .collection('BankApp_sessions')
          .doc(userId)
          .collection('paste_attempts')
          .doc(timestamp)
          .set({
        'timestamp': timestamp,
        'field': 'password',
        'detected': true,
      });
    } catch (e) {
      // Failed to log paste attempt - continue
    }
  }

  Future<void> uploadUserLocation(String userId, double latitude, double longitude, bool isWithinZone) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    await _firestore
        .collection('BankApp_sessions')
        .doc(userId)
        .collection('sessions')
        .doc('$timestamp')
        .set({
      'location': {
        'latitude': latitude,
        'longitude': longitude,
        'withinZone': isWithinZone,
        'timestamp': timestamp,
      },
    }, SetOptions(merge: true));
  }

Future<void> uploadBehavioralData(
  String userId,
  BehavioralData data, {
  Map<String, dynamic>? geolocation,
}) async {
  final trimmedId = userId.trim();
  if (trimmedId.isEmpty) return;

  try {
    final sessionId = DateTime.now().toIso8601String();
    final Map<String, dynamic> payload = data.toJson(); // ✅ Use model's method
    print('🧪 DEBUG behavioral payload: $payload');

     // if (geolocation != null) {
      //   payload['geolocation'] = geolocation;
      // }


    await _firestore
        .collection('BankApp_sessions')
        .doc(trimmedId)
        .collection('sessions')
        .doc(sessionId)
        .set(payload);


// if (geolocation != null) {
      //   await _firestore
      //       .collection('BankApp_sessions')
      //       .doc(trimmedId)
      //       .set({
      //     'geolocation': geolocation,
      //     'updatedAt': FieldValue.serverTimestamp(),
      //   }, SetOptions(merge: true));
      // } 
      
  } catch (e) {
    print('❌ Failed to upload behavioral data: $e');
  }
}


  void uploadForegroundDuration(String userId, int duration) {
    // Placeholder if needed later
  }

  Future<void> uploadTransactionData(String userId, Map<String, dynamic> transactionData) async {
    try {
      final trimmedId = userId.trim();
      if (trimmedId.isEmpty) return;

      final transactionId = transactionData['id'] ?? DateTime.now().toIso8601String();

      await _firestore
          .collection('BankApp_sessions')
          .doc(trimmedId)
          .collection('transactions')
          .doc(transactionId)
          .set({
        ...transactionData,
        'uploadedAt': FieldValue.serverTimestamp(),
      });

      print('✅ Transaction uploaded to Firebase: $transactionId');
    } catch (e) {
      print('❌ Failed to upload transaction: $e');
    }
  }

  Future<void> sendEmergencyAlert(String userId, String alertType, Map<String, dynamic> details) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      await _firestore
          .collection('emergency_alerts')
          .doc(timestamp)
          .set({
        'userId': userId,
        'alertType': alertType,
        'details': details,
        'timestamp': timestamp,
        'serverTimestamp': FieldValue.serverTimestamp(),
        'status': 'active',
      });

      await _firestore
          .collection('BankApp_sessions')
          .doc(userId)
          .collection('alerts')
          .doc(timestamp)
          .set({
        'alertType': alertType,
        'details': details,
        'timestamp': timestamp,
        'serverTimestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // Failed to send emergency alert - continue
    }
  }
}
