import 'package:flutter/material.dart';
import 'package:trust_chain_banking/firebase/firebase_service.dart';
import '../utils/constants.dart' as constants;
import '../routes/app_routes.dart';
import '../widgets/app_widgets.dart';
import '../services/auth_service.dart';
import '../services/gyro_tracker.dart';
import '../models/behavioral_data_model.dart';
import '../services/hold_angle_tracker.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final GyroTracker _gyroTracker = GyroTracker();
  final HoldAngleTracker _holdAngleTracker = HoldAngleTracker();
  Offset? _swipeStart;
  List<double> _swipeVelocities = [];
  List<Map<String, double>> _tapPositions = [];
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  List<int> _keystrokeIntervals = [];
  DateTime? _lastKeyTime;



  

double _calculateAverageSwipeVelocity() {
  if (_swipeVelocities.isEmpty) return 0.0;
  final total = _swipeVelocities.reduce((a, b) => a + b);
  return total / _swipeVelocities.length;
}


  @override
void initState() {
  super.initState();
  disableGyroTracking();
  _gyroTracker.startPassive(); // 🔁 Start passive gyro every 2s for 400ms
  _holdAngleTracker.start();
}

void _handleSwipeStart(DragStartDetails details) {
  _swipeStart = details.globalPosition;
}

void _handleSwipeEnd(DragEndDetails details) {
  final velocity = details.velocity.pixelsPerSecond.distance;
  _swipeVelocities.add(velocity);
  _swipeStart = null;
}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onPanStart: _handleSwipeStart,
        onPanEnd: _handleSwipeEnd,
         onTapDown: (details) {
      final pos = details.globalPosition;
      setState(() {
        _tapPositions.add({"x": pos.dx, "y": pos.dy});
      });
    },
      child: Scaffold(
        backgroundColor: constants.AppColors.bankingBackground,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Back button
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Title
                  const Text(
                    'Create Account',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    'Join SecureBank for safe and easy banking',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Full Name field
                  _buildTextField(
                    controller: _nameController,
                    label: 'Full Name',
                    icon: Icons.person_outline,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your full name';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      final now = DateTime.now();
if (_lastKeyTime != null) {
    final interval = now.difference(_lastKeyTime!).inMilliseconds;
    _keystrokeIntervals.add(interval);
  }
  _lastKeyTime = now;
},

                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Email field
                  _buildTextField(
                    controller: _emailController,
                    label: 'Email Address',
                    icon: Icons.email_outlined,
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                    onChanged: (value) {
  final now = DateTime.now();
  if (_lastKeyTime != null) {
    final diff = now.difference(_lastKeyTime!).inMilliseconds;
    _keystrokeIntervals.add(diff);
  }
  _lastKeyTime = now;
},

                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Phone field
                  _buildTextField(
                    controller: _phoneController,
                    label: 'Phone Number',
                    icon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your phone number';
                      }
                      if (value.length < 10) {
                        return 'Please enter a valid phone number';
                      }
                      return null;
                    },
                    onChanged: (value) {
  final now = DateTime.now();
  if (_lastKeyTime != null) {
    final diff = now.difference(_lastKeyTime!).inMilliseconds;
    _keystrokeIntervals.add(diff);
  }
  _lastKeyTime = now;
},

                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Password field
                  _buildTextField(
                    controller: _passwordController,
                    label: 'Password',
                    icon: Icons.lock_outline,
                    obscureText: _obscurePassword,
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      icon: Icon(
                        _obscurePassword ? Icons.visibility_off : Icons.visibility,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                    onChanged: (value) {
  final now = DateTime.now();
  if (_lastKeyTime != null) {
    final diff = now.difference(_lastKeyTime!).inMilliseconds;
    _keystrokeIntervals.add(diff);
  }
  _lastKeyTime = now;
},

                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Confirm Password field
                  _buildTextField(
                    controller: _confirmPasswordController,
                    label: 'Confirm Password',
                    icon: Icons.lock_outline,
                    obscureText: _obscureConfirmPassword,
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                      icon: Icon(
                        _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                    onChanged: (value) {
  final now = DateTime.now();
  if (_lastKeyTime != null) {
    final diff = now.difference(_lastKeyTime!).inMilliseconds;
    _keystrokeIntervals.add(diff);
  }
  _lastKeyTime = now;
},

                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Terms and conditions
                  Row(
                    children: [
                      Checkbox(
                        value: _agreeToTerms,
                        onChanged: (value) {
                          setState(() {
                            _agreeToTerms = value ?? false;
                          });
                        },
                        activeColor: constants.AppColors.bankingPrimary,
                      ),
                      Expanded(
                        child: Text(
                          'I agree to the Terms of Service and Privacy Policy',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 30),
                  
                  // Register button
                  AppButton(
                    text: 'Create Account',
                    isLoading: _isLoading,
                    onPressed: _agreeToTerms ? _handleRegister : null,
                    backgroundColor: constants.AppColors.bankingPrimary,
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Login link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Already have an account? ',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pushReplacementNamed(context, AppRoutes.login);
                        },
                        child: Text(
                          'Sign In',
                          style: TextStyle(
                            color: constants.AppColors.bankingPrimary,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      onTap: () {
      _gyroTracker.startBurst();// Start burst on field focus
    },
    onChanged: onChanged,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          color: Colors.white.withOpacity(0.7),
        ),
        prefixIcon: Icon(
          icon,
          color: Colors.white.withOpacity(0.7),
        ),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: constants.AppColors.bankingSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: constants.AppColors.bankingPrimary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: constants.AppColors.debitRed,
            width: 2,
          ),
        ),
      ),
    );
  }

  Future<void> _handleRegister() async {
  if (!_formKey.currentState!.validate()) return;

  setState(() {
    _isLoading = true;
  });

  try {
    // Final burst
    _gyroTracker.startBurst();
    await Future.delayed(const Duration(milliseconds: 500));
    final gyroData = _gyroTracker.consumeMagnitudes();
    final holdAngles = _holdAngleTracker.consumeHoldAngles();

    final double avgVelocity = _calculateAverageSwipeVelocity();

    final userData = {
      'name': _nameController.text.trim(),
      'email': _emailController.text.trim(),
      'phone': _phoneController.text.trim(),
      'password': _passwordController.text,
      // Behavioral features below
      'gyro_magnitude': gyroData,
      'keystroke_intervals': _keystrokeIntervals,
      'tap_positions': _tapPositions,
      'avg_swipe_velocity': avgVelocity,
    };
    print('🔢 Total keystroke intervals collected: ${_keystrokeIntervals.length}');
    print('⌨️ Keystroke intervals: $_keystrokeIntervals');



    final behavioralData = BehavioralData(
  gyroMagnitudes: gyroData,
  tapPositions: _tapPositions.map((pos) => Offset(pos['x']!, pos['y']!)).toList(),
  swipeVelocities: _swipeVelocities,
  keystrokeIntervals: _keystrokeIntervals,
  holdAngles: holdAngles,

);

await FirebaseService().uploadBehavioralData(
  _emailController.text.trim(),
  behavioralData,
);
GyroTracker().stop();

    final success = await AuthService.register(userData);

    if (success) {
      _showRegistrationSuccess();
    } else {
      _showError('Registration failed. Please try again.');
    }
  } catch (e) {
    _showError('An error occurred. Please try again.');
  } finally {
    setState(() {
      _isLoading = false;
    });
  }
}



  void _showRegistrationSuccess() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Color(0xFF10B981)),
              SizedBox(width: 8),
              Text(
                'Registration Successful!',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your Trust Chain Banking account has been created successfully.',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
              SizedBox(height: 12),
              Text(
                'Please login with your credentials to access your account.',
                style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 12),
              Text(
                'Note: Your account will be activated within 24 hours for security verification.',
                style: TextStyle(color: Color(0xFFFBBF24), fontSize: 13),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.pushReplacementNamed(context, AppRoutes.login);
              },
              child: const Text(
                'Continue to Login',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: constants.AppColors.debitRed,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _holdAngleTracker.stop();

    super.dispose();
  }
}
