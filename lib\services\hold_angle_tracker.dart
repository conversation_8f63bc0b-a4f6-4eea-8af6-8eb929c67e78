import 'dart:async';
import 'dart:math';
import 'package:sensors_plus/sensors_plus.dart';

class HoldAngleTracker {
  static final HoldAngleTracker _instance = HoldAngleTracker._init();
  factory HoldAngleTracker() => _instance;
  HoldAngleTracker._init();

  final List<double> _holdAngles = [];
  Timer? _timer;
  StreamSubscription<AccelerometerEvent>? _sub;
  bool _enabled = true;

  void start() {
    if (!_enabled) return;

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _sub?.cancel();
      _sub = accelerometerEvents.listen((event) {
        final angle = _calculateHoldAngle(event.x, event.y, event.z);
        _holdAngles.add(angle);
        print('📐 HOLDANGLE: $angle°');
      });

      Future.delayed(const Duration(milliseconds: 500), () {
        _sub?.cancel();
        _sub = null;
      });
    });
  }

  void stop() {
    _timer?.cancel();
    _sub?.cancel();
    _timer = null;
    _sub = null;
  }

  List<double> consumeHoldAngles() {
    final data = [..._holdAngles];
    _holdAngles.clear();
    return data;
  }

  double _calculateHoldAngle(double x, double y, double z) {
    final norm = sqrt(x * x + y * y + z * z);
    if (norm == 0) return 0;
    final pitch = asin(-x / norm); // rotation around X-axis
    final angleDegrees = pitch * (180 / pi);
    return angleDegrees.abs(); // make it positive
  }

  void disable() => _enabled = false;
  void enable() => _enabled = true;
}
