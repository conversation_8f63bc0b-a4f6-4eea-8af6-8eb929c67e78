import 'dart:async';

import 'package:flutter/material.dart';
import 'package:trust_chain_banking/firebase/firebase_service.dart';
import 'package:trust_chain_banking/models/behavioral_data_model.dart';
import 'package:trust_chain_banking/services/gyro_tracker.dart';
import '../utils/constants.dart' as constants;
import '../utils/currency_formatter.dart';
import '../routes/app_routes.dart';
import '../services/transaction_service.dart';
import '../models/transaction_model.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final TransactionService _transactionService = TransactionService();
  double _currentBalance = 0.0;
  List<Transaction> _recentTransactions = [];
  final String _userName = constants.BankingConstants.defaultUserName;
  final String _accountNumber = constants.BankingConstants.defaultAccountNumber;
  final String _bankName = constants.BankingConstants.defaultBankName;
  bool _isLoading = true;
  Offset? _swipeStart;
  DateTime? _swipeStartTime;
  List<double> _swipeVelocities = [];
  List<Map<String, double>> _tapPositions = [];
  Timer? _uploadTimer;
  String? _sessionId;



  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    disableGyroTracking();
     GyroTracker().startPassive(); // Start passive gyro tracking
     
  }

  void _handleTapDown(TapDownDetails details) {
  final position = details.globalPosition;
  _tapPositions.add({
    'x': position.dx,
    'y': position.dy,
  });
}

Future<void> _recordBehavioralData() async {
  final data = BehavioralData(
    tapPositions: _tapPositions.map((pos) => Offset(pos['x']!, pos['y']!)).toList(),
    swipeVelocities: _swipeVelocities,
    gyroMagnitudes: GyroTracker().consumeMagnitudes(),
  );

  await FirebaseService().uploadBehavioralData(_userName, data);
}


  void _handleSwipeStart(DragStartDetails details) {
  _swipeStart = details.localPosition;
  _swipeStartTime = DateTime.now();
}

void _handleSwipeEnd(DragEndDetails details) {
  if (_swipeStart == null || _swipeStartTime == null) return;

  final endTime = DateTime.now();
  final durationMs = endTime.difference(_swipeStartTime!).inMilliseconds;
  if (durationMs > 0) {
    final velocity = details.velocity.pixelsPerSecond.distance;
    final seconds = durationMs / 1000;
    final avgVelocity = velocity / seconds;
    _swipeVelocities.add(avgVelocity);
  }

  _swipeStart = null;
  _swipeStartTime = null;
}


  Future<void> _loadDashboardData() async {
    try {
      await _transactionService.initialize();
      setState(() {
        _currentBalance = _transactionService.getCurrentBalance();
        _recentTransactions = _transactionService.getRecentTransactions(limit: 4);
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
          onPanStart: _handleSwipeStart,
          onPanEnd: _handleSwipeEnd,
              behavior: HitTestBehavior.translucent,
             onTapDown: _handleTapDown,
      child: Scaffold(
        backgroundColor: constants.AppColors.bankingBackground,
        body: SafeArea(
          child: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              GyroTracker().startBurst(); // Trigger burst on scroll/swipe
              return false;
              },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildBalanceCard(),
                  const SizedBox(height: 24),
                  _buildQuickActions(),
                  const SizedBox(height: 24),
                  _buildQuickSend(),
                  const SizedBox(height: 24),
                  _buildRecentTransactions(),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomNavigation(),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // User Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                constants.AppColors.bankingPrimary,
                constants.AppColors.bankingSecondary,
              ],
            ),
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),

        // Welcome Text
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              Text(
                _userName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Emergency & Notification Icons
        Row(
          children: [
            GestureDetector(
              onTap: () => _showEmergencyOptions(),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFDC2626),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emergency,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: constants.AppColors.bankingSurface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 20,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            constants.AppColors.bankingPrimary,
            constants.AppColors.bankingPrimary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: constants.AppColors.bankingPrimary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Balance',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              Text(
                _bankName,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Text(
            _currentBalance.inr,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Card Number
          Row(
            children: [
              const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _accountNumber,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              // Mastercard logo placeholder
              Container(
                width: 40,
                height: 24,
                decoration: BoxDecoration(
                  color: constants.AppColors.bankingAccent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'MC',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: Icons.send,
              label: 'Send',
              onTap: () {
                _showSendMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.request_page,
              label: 'Request',
              onTap: () {
                _showRequestMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.add_circle_outline,
              label: 'Top Up',
              onTap: () {
                _showTopUpDialog();
              },
            ),
          ],
        ),
        const SizedBox(height: 15),
        // 🚀 HACKATHON: ICP and Masumi Demo Button
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.icpMasumiDemo);
            },
            icon: const Icon(Icons.rocket_launch, color: Colors.white),
            label: const Text(
              '🚀 ICP & Masumi Integration Demo',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Trigger burst on tap
        onTap(); // Existing callback
        },
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: constants.AppColors.bankingSurface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: constants.AppColors.bankingPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: constants.AppColors.bankingPrimary,
                size: 20,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Quick Send',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all contacts
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        SizedBox(
          height: 80,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildContactAvatar('Add', Icons.add, isAddButton: true),
              _buildContactAvatar('Priya', Icons.person),
              _buildContactAvatar('Rahul', Icons.person),
              _buildContactAvatar('Anita', Icons.person),
              _buildContactAvatar('Vikram', Icons.person),
              _buildContactAvatar('Meera', Icons.person),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactAvatar(String name, IconData icon, {bool isAddButton = false}) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: GestureDetector(
        onTap: () {
          if (isAddButton) {
            // Navigate to add contact
          } else {
            // Navigate to send money to contact
          }
        },
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isAddButton
                    ? constants.AppColors.bankingSurface
                    : constants.AppColors.bankingPrimary.withOpacity(0.2),
                shape: BoxShape.circle,
                border: isAddButton
                    ? Border.all(
                        color: constants.AppColors.bankingPrimary.withOpacity(0.3),
                        style: BorderStyle.solid,
                      )
                    : null,
              ),
              child: Icon(
                icon,
                color: isAddButton
                    ? constants.AppColors.bankingPrimary
                    : constants.AppColors.bankingPrimary,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Transaction',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.activity);
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Dynamic transaction list
        if (_recentTransactions.isEmpty)
          const Center(
            child: Text(
              'No recent transactions',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          )
        else
          ...(_recentTransactions.map((transaction) => Column(
            children: [
              _buildTransactionItem(
                title: transaction.title,
                subtitle: transaction.description,
                amount: transaction.amount,
                isCredit: transaction.isCredit,
                icon: transaction.icon,
                date: transaction.formattedDate,
              ),
              if (transaction != _recentTransactions.last)
                const SizedBox(height: 12),
            ],
          )).toList()),
      ],
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required String subtitle,
    required double amount,
    required bool isCredit,
    required IconData icon,
    String? date,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCredit
                  ? constants.AppColors.creditGreen.withOpacity(0.2)
                  : constants.AppColors.debitRed.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isCredit
                  ? constants.AppColors.creditGreen
                  : constants.AppColors.debitRed,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                  ),
                ),
                if (date != null)
                  Text(
                    date!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.4),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),

          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isCredit ? '+' : '-'}${amount.inr}',
                style: TextStyle(
                  color: isCredit
                      ? constants.AppColors.creditGreen
                      : constants.AppColors.debitRed,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(Icons.home, 'Home', true),
          _buildNavItem(Icons.bar_chart, 'Activity', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.activity);
          }),
          _buildNavItem(Icons.favorite_border, 'Favorites', false, onTap: () {
            _showFavoritesBottomSheet();
          }),
          _buildNavItem(Icons.person_outline, 'Profile', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.profile);
          }),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Burst on nav icon tap
        if (onTap != null) onTap();
        },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isActive
                ? constants.AppColors.bankingPrimary
                : Colors.white.withOpacity(0.5),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isActive
                  ? constants.AppColors.bankingPrimary
                  : Colors.white.withOpacity(0.5),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _showSendMoneyDialog() {
    GyroTracker().startBurst(); // Trigger burst sample
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.send, color: Color(0xFF3B82F6)),
              SizedBox(width: 8),
              Text(
                'Send Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Send Money feature will be available soon. You can transfer money to any UPI ID, bank account, or mobile number.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showRequestMoneyDialog() {
    GyroTracker().startBurst();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.request_page, color: Color(0xFF10B981)),
              SizedBox(width: 8),
              Text(
                'Request Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Request Money feature will be available soon. You can request money from contacts via SMS, WhatsApp, or email.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF10B981)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTopUpDialog() {
    GyroTracker().startBurst();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.add_circle_outline, color: Color(0xFFFBBF24)),
              SizedBox(width: 8),
              Text(
                'Add Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Add Money feature will be available soon. You can add money from your linked bank accounts, debit cards, or UPI.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFFFBBF24)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showEmergencyOptions() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.emergency, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Emergency Options',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildEmergencyOption(
                icon: Icons.block,
                title: 'Block Account',
                subtitle: 'Temporarily block all transactions',
                color: const Color(0xFFDC2626),
                onTap: () => _blockAccount(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.phone,
                title: 'Emergency Contact',
                subtitle: 'Call bank emergency helpline',
                color: const Color(0xFFFBBF24),
                onTap: () => _callEmergencyHelpline(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.security,
                title: 'Security Alert',
                subtitle: 'Report suspicious activity',
                color: const Color(0xFF3B82F6),
                onTap: () => _reportSuspiciousActivity(),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmergencyOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _blockAccount() {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Block Account',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Are you sure you want to block your account? This will prevent all transactions until you contact the bank.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmAccountBlock();
              },
              child: const Text(
                'Block Account',
                style: TextStyle(color: Color(0xFFDC2626)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _confirmAccountBlock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account blocked successfully. Contact bank to unblock.'),
        backgroundColor: Color(0xFFDC2626),
      ),
    );
  }

  void _callEmergencyHelpline() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Emergency Helpline: 1800-123-4567'),
        backgroundColor: Color(0xFFFBBF24),
      ),
    );
  }

  void _reportSuspiciousActivity() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Security alert sent. Bank will contact you shortly.'),
        backgroundColor: Color(0xFF3B82F6),
      ),
    );
  }

  // Show favorites bottom sheet
  void _showFavoritesBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 12),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Favorite Transactions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildFavoriteItem(
                    'UPI to Priya Sharma',
                    'Rent Payment',
                    Icons.home,
                    '₹25,000',
                  ),
                  _buildFavoriteItem(
                    'Electricity Bill',
                    'MSEB Payment',
                    Icons.flash_on,
                    '₹3,200',
                  ),
                  _buildFavoriteItem(
                    'Amazon Purchase',
                    'Quick Shopping',
                    Icons.shopping_bag,
                    'Variable',
                  ),
                  _buildFavoriteItem(
                    'Petrol - Shell',
                    'Vehicle Fuel',
                    Icons.local_gas_station,
                    '₹2,000',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteItem(String title, String subtitle, IconData icon, String amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: constants.AppColors.bankingPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: constants.AppColors.bankingPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  @override
void dispose() {
  GyroTracker().stopPassive(); // Stop background tracking
  GyroTracker().stopBurst();
  _recordBehavioralData();

  final magnitudes = GyroTracker().consumeMagnitudes();
  print('📤 Dashboard gyro data: $magnitudes');

  // TODO: Upload to Firebase if needed

  super.dispose();
}

}