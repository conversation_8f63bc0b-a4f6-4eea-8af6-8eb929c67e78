import 'dart:ui';
import 'dart:math' as math;

class BehavioralData {
  List<int> keystrokeTimes = [];
  List<int> keystrokeIntervals = []; // Direct intervals for model training
  List<String> keystrokeCharacters = []; // Characters typed (for sequence analysis)
  List<double> keystrokePressureLevels = []; // Pressure data if available
  List<int> dwellTimes = []; // How long keys are held down
  List<Offset> tapPositions = [];
  List<double> swipeVelocities = [];
  List<int> swipeDurations = [];
  List<double> gyroMagnitudes = []; // Added gyroscope data
  int sessionStartTime = DateTime.now().millisecondsSinceEpoch;
  final List<double>? holdAngles;


  BehavioralData({
    this.gyroMagnitudes = const [],
    this.holdAngles,
    this.keystrokeIntervals = const [],
    this.tapPositions = const [],
    this.swipeVelocities = const [],
  });

  // Enhanced keystroke data for ML training
  // void addDetailedKeystroke({
  //   required int timestamp,
  //   String? character,
  //   double? pressure,
  //   int? dwellTime,
  // }) {
  //   keystrokeTimes.add(timestamp);
    
  //   // Calculate interval from previous keystroke
  //   if (keystrokeTimes.length > 1) {
  //     final interval = keystrokeTimes.last - keystrokeTimes[keystrokeTimes.length - 2];
  //     keystrokeIntervals.add(interval);
  //   }
    
  //   if (character != null) {
  //     keystrokeCharacters.add(character);
  //   }
    
  //   if (pressure != null) {
  //     keystrokePressureLevels.add(pressure);
  //   }
    
  //   if (dwellTime != null) {
  //     dwellTimes.add(dwellTime);
  //   }
  // }

  // Computed property for keystroke timings compatibility

  List<int> get keystrokeTimings => keystrokeIntervals;

  // void addKeystroke(int timestamp) {
  //   addDetailedKeystroke(timestamp: timestamp);
  // }

  // void addTapData(double pressure, Offset position) {
  //   tapPositions.add(position);
  //   if (keystrokePressureLevels.isNotEmpty && keystrokePressureLevels.length == keystrokeTimes.length) {
  //     // Update pressure for last keystroke if it matches
  //     keystrokePressureLevels[keystrokePressureLevels.length - 1] = pressure;
  //   }
  // }

  void addSwipeVelocity(double velocity) {
    swipeVelocities.add(velocity);
  }

  void addSwipeDuration(int duration) {
    swipeDurations.add(duration);
  }

  // Enhanced ML training data export

  Map<String, dynamic> toJson() {
    return {
      'keystroke_intervals': keystrokeIntervals,
      // 'keystroke_times': keystrokeTimes,
      // 'keystroke_characters': keystrokeCharacters,
      // 'keystroke_pressure_levels': keystrokePressureLevels,
      // 'dwell_times': dwellTimes,
      'tap_positions': tapPositions.map((pos) => {'x': pos.dx, 'y': pos.dy}).toList(),
      'avg_swipe_velocity': swipeVelocities.isEmpty ? 0 : swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length,
      'hold_angles': holdAngles,
      'session_duration': DateTime.now().millisecondsSinceEpoch - sessionStartTime,
      'gyro_magnitudes': gyroMagnitudes,
      // ML training specific metrics

      // 'typing_rhythm_features': _calculateTypingRhythmFeatures(),
      // 'pressure_variation': _calculatePressureVariation(),
      // 'timing_patterns': _calculateTimingPatterns(),
    };
  }

  // // ML Training Features

  // Map<String, dynamic> _calculateTypingRhythmFeatures() {
  //   if (keystrokeIntervals.length < 3) return {};
    
  //   final avgInterval = keystrokeIntervals.reduce((a, b) => a + b) / keystrokeIntervals.length;
  //   final variance = keystrokeIntervals.map((x) => (x - avgInterval) * (x - avgInterval)).reduce((a, b) => a + b) / keystrokeIntervals.length;
  //   final stdDev = math.sqrt(variance);
    
  //   return {
  //     'avg_keystroke_interval': avgInterval,
  //     'keystroke_interval_variance': variance,
  //     'keystroke_interval_std_dev': stdDev,
  //     'typing_speed_wpm': keystrokeIntervals.isEmpty ? 0 : (60000 / avgInterval) * 5, // Approximate WPM
  //     'rhythm_consistency': stdDev / avgInterval, // Lower is more consistent
  //   };
  // }

  // Map<String, dynamic> _calculatePressureVariation() {
  //   if (keystrokePressureLevels.length < 2) return {};
    
  //   final avgPressure = keystrokePressureLevels.reduce((a, b) => a + b) / keystrokePressureLevels.length;
  //   final pressureVariance = keystrokePressureLevels.map((x) => (x - avgPressure) * (x - avgPressure)).reduce((a, b) => a + b) / keystrokePressureLevels.length;
    
  //   return {
  //     'avg_pressure': avgPressure,
  //     'pressure_variance': pressureVariance,
  //     'pressure_range': keystrokePressureLevels.isEmpty ? 0 : keystrokePressureLevels.reduce(math.max) - keystrokePressureLevels.reduce(math.min),
  //   };
  // }

  // Map<String, dynamic> _calculateTimingPatterns() {
  //   if (keystrokeIntervals.length < 5) return {};
    
  //   // Digraph timing (common letter pairs)
  //   Map<String, List<int>> digraphTimings = {};
  //   for (int i = 0; i < keystrokeCharacters.length - 1 && i < keystrokeIntervals.length; i++) {
  //     final digraph = keystrokeCharacters[i] + keystrokeCharacters[i + 1];
  //     digraphTimings[digraph] ??= [];
  //     digraphTimings[digraph]!.add(keystrokeIntervals[i]);
  //   }
    
  //   return {
  //     'digraph_timings': digraphTimings.map((key, value) => MapEntry(key, value.reduce((a, b) => a + b) / value.length)),
  //     'total_keystrokes': keystrokeTimes.length,
  //     'unique_digraphs': digraphTimings.length,
  //   };
  // }
}
