import 'dart:async';
import 'dart:math';
import 'package:sensors_plus/sensors_plus.dart';


void disableGyroTracking() {
  GyroTracker()._enabled = false;
  GyroTracker().stop(); // Stop ongoing sampling immediately
}

void enableGyroTracking() {
  GyroTracker()._enabled = true;
}


class GyroTracker {
  static final GyroTracker _instance = GyroTracker._init();
  factory GyroTracker() => _instance;
  GyroTracker._init();
  bool _enabled = true; // Set to false to disable all gyro tracking

  final List<double> _gyroMagnitudes = [];

  StreamSubscription<GyroscopeEvent>? _burstSub;
  StreamSubscription<GyroscopeEvent>? _passiveSub;
  Timer? _passiveTimer;

  /// Starts 3-second burst sampling on interaction
  void startBurst() {
  if (!_enabled) return; // ← Add this check

  _burstSub?.cancel();

  _burstSub = gyroscopeEvents.listen((event) {
    final mag = _calculateMagnitude(event.x, event.y, event.z);
    _gyroMagnitudes.add(mag);
  });

  Future.delayed(const Duration(seconds: 3), stopBurst);
}


  void stopBurst() {
    _burstSub?.cancel();
    _burstSub = null;
  }

 
  void stopPassive() {
    _passiveTimer?.cancel();
    _passiveTimer = null;

    _passiveSub?.cancel();
    _passiveSub = null;
  } /// Starts passive sampling every 2 seconds
  void startPassive() {
  if (!_enabled) return; // ← Add this check

  _passiveTimer?.cancel();

  _passiveTimer = Timer.periodic(const Duration(seconds: 2), (_) {
    _passiveSub?.cancel();
    _passiveSub = gyroscopeEvents.listen((event) {
      final mag = _calculateMagnitude(event.x, event.y, event.z);
      _gyroMagnitudes.add(mag);
      print('📈 Gyro sample: $mag');
    });

    Future.delayed(const Duration(seconds: 1), () {
      _passiveSub?.cancel();
      _passiveSub = null;
    });
  });
}

  /// Stops all gyroscope tracking (both burst and passive)
void stop() {
  stopBurst();
  stopPassive();
}

  /// Call when uploading to Firebase
  List<double> consumeMagnitudes() {
    final data = [..._gyroMagnitudes];
    _gyroMagnitudes.clear();
    return data;
  }

  double _calculateMagnitude(double x, double y, double z) {
    return sqrt(x * x + y * y + z * z);
  }
}
